"use client"

import * as React from "react"
import { Check, ExternalLink, GitBranch, Search, X } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"

/**
 * Represents a repository option
 */
export interface Repository {
  url: string
  name: string
  owner: string
  description?: string
  isPrivate?: boolean
  lastUpdated?: Date
}

/**
 * Props for the RepositorySelector component
 */
export interface RepositorySelectorProps {
  /**
   * Current repository URL value
   */
  value?: string
  
  /**
   * Callback fired when repository selection changes
   */
  onChange?: (url: string) => void
  
  /**
   * Whether the input is disabled
   */
  disabled?: boolean
  
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Recently used repositories
   */
  recentRepositories?: Repository[]
  
  /**
   * Suggested repositories
   */
  suggestedRepositories?: Repository[]
  
  /**
   * Placeholder text for the input
   */
  placeholder?: string
  
  /**
   * Whether to show validation feedback
   */
  showValidation?: boolean
}

/**
 * RepositorySelector Component
 * 
 * GitHub repository URL input with validation and selection features:
 * - URL validation for GitHub repositories
 * - Recent repositories dropdown
 * - Repository suggestions
 * - Visual feedback for valid/invalid URLs
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <RepositorySelector
 *   value="https://github.com/owner/repo"
 *   onChange={(url) => console.log('Selected:', url)}
 *   recentRepositories={[
 *     { url: "https://github.com/owner/repo1", name: "repo1", owner: "owner" },
 *     { url: "https://github.com/owner/repo2", name: "repo2", owner: "owner" }
 *   ]}
 * />
 * ```
 */
export function RepositorySelector({
  value = "",
  onChange,
  disabled = false,
  className,
  recentRepositories = [],
  suggestedRepositories = [],
  placeholder = "https://github.com/owner/repository",
  showValidation = true,
}: RepositorySelectorProps) {
  const [isOpen, setIsOpen] = React.useState(false)
  const [inputValue, setInputValue] = React.useState(value)
  
  // Validate GitHub URL
  const validateGitHubUrl = React.useCallback((url: string): boolean => {
    if (!url.trim()) return true // Empty is valid (optional field)
    
    try {
      const urlObj = new URL(url)
      return urlObj.hostname === "github.com" && 
             urlObj.pathname.split("/").filter(Boolean).length >= 2
    } catch {
      return false
    }
  }, [])
  
  // Parse repository info from URL
  const parseRepositoryInfo = React.useCallback((url: string): Repository | null => {
    try {
      const urlObj = new URL(url)
      if (urlObj.hostname !== "github.com") return null
      
      const pathParts = urlObj.pathname.split("/").filter(Boolean)
      if (pathParts.length < 2) return null
      
      const [owner, name] = pathParts
      return {
        url,
        owner,
        name,
      }
    } catch {
      return null
    }
  }, [])
  
  const isValid = validateGitHubUrl(inputValue)
  const repositoryInfo = parseRepositoryInfo(inputValue)
  
  // Handle input changes
  const handleInputChange = React.useCallback((newValue: string) => {
    setInputValue(newValue)
    onChange?.(newValue)
  }, [onChange])
  
  // Handle repository selection from dropdown
  const handleRepositorySelect = React.useCallback((repository: Repository) => {
    setInputValue(repository.url)
    onChange?.(repository.url)
    setIsOpen(false)
  }, [onChange])
  
  // Clear selection
  const handleClear = React.useCallback(() => {
    setInputValue("")
    onChange?.("")
  }, [onChange])
  
  // Sync with external value changes
  React.useEffect(() => {
    if (value !== inputValue) {
      setInputValue(value)
    }
  }, [value]) // Intentionally not including inputValue to avoid infinite loop
  
  // Combine recent and suggested repositories
  const allRepositories = React.useMemo(() => {
    const recent = recentRepositories.map(repo => ({ ...repo, type: "recent" as const }))
    const suggested = suggestedRepositories.map(repo => ({ ...repo, type: "suggested" as const }))
    
    // Remove duplicates based on URL
    const seen = new Set<string>()
    return [...recent, ...suggested].filter(repo => {
      if (seen.has(repo.url)) return false
      seen.add(repo.url)
      return true
    })
  }, [recentRepositories, suggestedRepositories])
  
  return (
    <div className={className}>
      <Label htmlFor="repository-input" className="text-sm font-medium">
        Repository (Optional)
      </Label>
      
      <div className="mt-2 space-y-2">
        <div className="relative">
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Input
                id="repository-input"
                type="url"
                placeholder={placeholder}
                value={inputValue}
                onChange={(e) => handleInputChange(e.target.value)}
                disabled={disabled}
                className={cn(
                  "pr-20",
                  showValidation && inputValue && !isValid && "border-destructive focus-visible:ring-destructive/20",
                  showValidation && inputValue && isValid && "border-green-500 focus-visible:ring-green-500/20"
                )}
                aria-describedby="repository-description repository-validation"
              />
              
              {/* Clear button */}
              {inputValue && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={handleClear}
                  disabled={disabled}
                  className="absolute right-10 top-0 h-9 w-9"
                  aria-label="Clear repository"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
              
              {/* External link button */}
              {inputValue && isValid && (
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  onClick={() => window.open(inputValue, "_blank", "noopener,noreferrer")}
                  disabled={disabled}
                  className="absolute right-1 top-0 h-9 w-9"
                  aria-label="Open repository in new tab"
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              )}
            </div>
            
            {/* Repository selector dropdown */}
            {allRepositories.length > 0 && (
              <Popover open={isOpen} onOpenChange={setIsOpen}>
                <PopoverTrigger asChild>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    disabled={disabled}
                    aria-label="Select from recent repositories"
                    className="h-9 w-9 shrink-0"
                  >
                    <Search className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="end">
                  <Command>
                    <CommandInput placeholder="Search repositories..." />
                    <CommandList>
                      <CommandEmpty>No repositories found.</CommandEmpty>
                      
                      {recentRepositories.length > 0 && (
                        <CommandGroup heading="Recent">
                          {recentRepositories.map((repo) => (
                            <CommandItem
                              key={repo.url}
                              value={`${repo.owner}/${repo.name}`}
                              onSelect={() => handleRepositorySelect(repo)}
                              className="flex items-center justify-between"
                            >
                              <div className="flex items-center gap-2">
                                <GitBranch className="h-4 w-4" />
                                <div>
                                  <div className="font-medium">{repo.owner}/{repo.name}</div>
                                  {repo.description && (
                                    <div className="text-sm text-muted-foreground truncate">
                                      {repo.description}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {repo.isPrivate && (
                                <Badge variant="secondary" className="text-xs">
                                  Private
                                </Badge>
                              )}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      )}
                      
                      {suggestedRepositories.length > 0 && (
                        <>
                          {recentRepositories.length > 0 && <Separator />}
                          <CommandGroup heading="Suggested">
                            {suggestedRepositories.map((repo) => (
                              <CommandItem
                                key={repo.url}
                                value={`${repo.owner}/${repo.name}`}
                                onSelect={() => handleRepositorySelect(repo)}
                                className="flex items-center justify-between"
                              >
                                <div className="flex items-center gap-2">
                                  <GitBranch className="h-4 w-4" />
                                  <div>
                                    <div className="font-medium">{repo.owner}/{repo.name}</div>
                                    {repo.description && (
                                      <div className="text-sm text-muted-foreground truncate">
                                        {repo.description}
                                      </div>
                                    )}
                                  </div>
                                </div>
                                {repo.isPrivate && (
                                  <Badge variant="secondary" className="text-xs">
                                    Private
                                  </Badge>
                                )}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </>
                      )}
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>
        
        {/* Repository info display */}
        {repositoryInfo && isValid && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <GitBranch className="h-4 w-4" />
            <span>{repositoryInfo.owner}/{repositoryInfo.name}</span>
            <Check className="h-4 w-4 text-green-500" />
          </div>
        )}
        
        {/* Validation feedback */}
        {showValidation && inputValue && !isValid && (
          <p id="repository-validation" className="text-sm text-destructive">
            Please enter a valid GitHub repository URL (e.g., https://github.com/owner/repo)
          </p>
        )}
        
        {/* Description */}
        <p id="repository-description" className="text-sm text-muted-foreground">
          Specify a particular repository to focus the query on. Leave empty to search across all ingested repositories.
        </p>
      </div>
    </div>
  )
}
