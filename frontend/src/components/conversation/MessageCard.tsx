"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Thum<PERSON>Down, Thum<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Zap } from "lucide-react"
import ReactMarkdown from "react-markdown"
import remarkGfm from "remark-gfm"
import rehypeHighlight from "rehype-highlight"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"

import { useCopyToClipboard } from "@/hooks/useCopyToClipboard"
import { useToast } from "@/hooks/useToast"
import { cn } from "@/lib/utils"
import type { ConversationMessage } from "@/types/api"

/**
 * Props for the MessageCard component
 */
export interface MessageCardProps {
  /**
   * The conversation message to display
   */
  message: ConversationMessage
  
  /**
   * Index of the message in the conversation
   */
  index: number
  
  /**
   * Whether to show timestamps
   */
  showTimestamp?: boolean
  
  /**
   * Whether to show message actions
   */
  showActions?: boolean
  
  /**
   * Whether to show confidence scores for assistant messages
   */
  showConfidence?: boolean
  
  /**
   * Whether to show processing time for assistant messages
   */
  showProcessingTime?: boolean
  
  /**
   * Whether to show source citations
   */
  showSources?: boolean
  
  /**
   * Custom CSS class name
   */
  className?: string
  
  /**
   * Callback fired when message is copied
   */
  onCopy?: (content: string) => void
  
  /**
   * Callback fired when message is rated
   */
  onRate?: (rating: "up" | "down") => void
  
  /**
   * Callback fired when message actions are triggered
   */
  onAction?: (action: string) => void
}

/**
 * MessageCard Component
 * 
 * Individual message display component with features:
 * - User/assistant message differentiation
 * - Markdown rendering for assistant responses
 * - Timestamp and metadata display
 * - Copy to clipboard functionality
 * - Message rating (thumbs up/down)
 * - Source citations display
 * - Confidence scores and processing time
 * - Accessibility support
 * 
 * @example
 * ```tsx
 * <MessageCard
 *   message={{
 *     type: "assistant",
 *     content: "# Authentication System\n\nThe system uses JWT tokens...",
 *     metadata: {
 *       timestamp: new Date(),
 *       agent_type: "TECHNICAL_ARCHITECT",
 *       confidence: 0.95,
 *       sources: ["src/auth/jwt.py:1-50"]
 *     }
 *   }}
 *   index={0}
 *   showTimestamp={true}
 *   showActions={true}
 *   onCopy={(content) => console.log('Copied:', content)}
 * />
 * ```
 */
export function MessageCard({
  message,
  index,
  showTimestamp = true,
  showActions = true,
  showConfidence = true,
  showProcessingTime = true,
  showSources = true,
  className,
  onCopy,
  onRate,
  onAction,
}: MessageCardProps) {
  const { toast } = useToast()
  const { copyToClipboard } = useCopyToClipboard()
  
  const isUser = message.type === "user"
  const isAssistant = message.type === "assistant"
  const timestamp = message.metadata?.timestamp ? new Date(message.metadata.timestamp) : null
  const agentType = message.metadata?.agent_type
  const confidence = message.metadata?.confidence
  const processingTime = message.metadata?.processing_time
  const sources = message.metadata?.sources || []
  
  // Handle copy to clipboard
  const handleCopy = React.useCallback(async () => {
    try {
      await copyToClipboard(message.content)
      toast({
        title: "Copied to clipboard",
        description: "Message content has been copied",
      })
      onCopy?.(message.content)
    } catch (error) {
      toast({
        title: "Failed to copy",
        description: "Could not copy message content",
        variant: "destructive",
      })
    }
  }, [message.content, copyToClipboard, toast, onCopy])
  
  // Handle message rating
  const handleRate = React.useCallback((rating: "up" | "down") => {
    toast({
      title: `Message rated ${rating === "up" ? "positively" : "negatively"}`,
      description: "Thank you for your feedback",
    })
    onRate?.(rating)
  }, [toast, onRate])
  
  // Format timestamp
  const formatTimestamp = React.useCallback((date: Date) => {
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / 60000)
    
    if (diffInMinutes < 1) return "Just now"
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    
    return date.toLocaleString()
  }, [])
  
  // Get agent icon and color
  const getAgentInfo = React.useCallback((type?: string) => {
    switch (type) {
      case "TECHNICAL_ARCHITECT":
        return { icon: Zap, color: "text-blue-500", label: "Technical Architect" }
      case "TASK_PLANNER":
        return { icon: Clock, color: "text-green-500", label: "Task Planner" }
      case "RAG_RETRIEVAL":
        return { icon: Bot, color: "text-purple-500", label: "RAG Retrieval" }
      default:
        return { icon: Bot, color: "text-muted-foreground", label: "Assistant" }
    }
  }, [])
  
  const agentInfo = getAgentInfo(agentType)
  const AgentIcon = agentInfo.icon
  
  return (
    <Card className={cn(
      "group relative transition-all duration-200",
      isUser && "ml-8 bg-primary/5 border-primary/20",
      isAssistant && "mr-8",
      className
    )}>
      <CardContent className="p-4">
        {/* Message Header */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            {/* Avatar */}
            <div className={cn(
              "flex items-center justify-center w-8 h-8 rounded-full",
              isUser ? "bg-primary text-primary-foreground" : "bg-muted"
            )}>
              {isUser ? (
                <User className="w-4 h-4" />
              ) : (
                <AgentIcon className={cn("w-4 h-4", agentInfo.color)} />
              )}
            </div>
            
            {/* Message info */}
            <div className="flex items-center gap-2">
              <span className="font-medium text-sm">
                {isUser ? "You" : agentInfo.label}
              </span>
              
              {agentType && (
                <Badge variant="outline" className="text-xs">
                  {agentType.replace("_", " ")}
                </Badge>
              )}
              
              {showTimestamp && timestamp && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span className="text-xs text-muted-foreground">
                        {formatTimestamp(timestamp)}
                      </span>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{timestamp.toLocaleString()}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
          </div>
          
          {/* Actions */}
          {showActions && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label="Message actions"
                >
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleCopy}>
                  <Copy className="mr-2 h-4 w-4" />
                  Copy message
                </DropdownMenuItem>
                
                {isAssistant && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleRate("up")}>
                      <ThumbsUp className="mr-2 h-4 w-4" />
                      Helpful
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleRate("down")}>
                      <ThumbsDown className="mr-2 h-4 w-4" />
                      Not helpful
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
        
        {/* Message Content */}
        <div className="space-y-3">
          {isUser ? (
            <div className="text-sm leading-relaxed whitespace-pre-wrap">
              {message.content}
            </div>
          ) : (
            <div className="prose prose-sm max-w-none dark:prose-invert">
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeHighlight]}
                components={{
                  // Customize code blocks
                  code: ({ node, inline, className, children, ...props }) => {
                    const match = /language-(\w+)/.exec(className || "")
                    return !inline && match ? (
                      <pre className={className} {...props}>
                        <code>{children}</code>
                      </pre>
                    ) : (
                      <code className={className} {...props}>
                        {children}
                      </code>
                    )
                  },
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          )}
        </div>
        
        {/* Message Metadata */}
        {isAssistant && (showConfidence || showProcessingTime || (showSources && sources.length > 0)) && (
          <>
            <Separator className="my-3" />
            <div className="flex flex-wrap items-center gap-4 text-xs text-muted-foreground">
              {showConfidence && confidence !== undefined && (
                <div className="flex items-center gap-1">
                  <span>Confidence:</span>
                  <Badge variant="outline" className="text-xs">
                    {Math.round(confidence * 100)}%
                  </Badge>
                </div>
              )}
              
              {showProcessingTime && processingTime !== undefined && (
                <div className="flex items-center gap-1">
                  <Clock className="w-3 h-3" />
                  <span>{processingTime.toFixed(2)}s</span>
                </div>
              )}
              
              {showSources && sources.length > 0 && (
                <div className="flex items-center gap-1">
                  <span>Sources:</span>
                  <span>{sources.length}</span>
                </div>
              )}
            </div>
          </>
        )}
        
        {/* Source Citations */}
        {showSources && sources.length > 0 && (
          <div className="mt-3 space-y-1">
            <span className="text-xs font-medium text-muted-foreground">Sources:</span>
            <div className="flex flex-wrap gap-1">
              {sources.slice(0, 3).map((source, idx) => (
                <Badge key={idx} variant="secondary" className="text-xs font-mono">
                  {source}
                </Badge>
              ))}
              {sources.length > 3 && (
                <Badge variant="secondary" className="text-xs">
                  +{sources.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
